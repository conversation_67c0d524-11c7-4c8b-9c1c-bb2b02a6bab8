@echo off
echo Requesting administrator privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Administrator privileges confirmed.
) else (
    echo Requesting administrator privileges...
    powershell "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && \"%~f0\"' -Verb RunAs"
    exit /b
)

echo.
echo Assigning drive letters to external drives...
echo.

(
echo select disk 5
echo select partition 2
echo assign letter=D
echo select disk 4  
echo select partition 2
echo assign letter=E
echo select disk 3
echo select partition 2  
echo assign letter=F
echo select disk 6
echo select partition 1
echo assign letter=G
echo exit
) | diskpart

echo.
echo Drive letter assignment complete!
echo.
echo Checking assigned drives:
wmic logicaldisk get deviceid,volumename,size
echo.
pause 