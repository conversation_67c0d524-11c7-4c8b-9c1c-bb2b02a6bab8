@echo off
echo Requesting administrator privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Administrator privileges confirmed.
) else (
    echo Requesting administrator privileges...
    powershell "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && \"%~f0\"' -Verb RunAs"
    exit /b
)

echo.
echo Assigning drive letters to external drives...
echo.

REM Current disk configuration based on Get-Disk output:
REM Disk 2: UnionSine USB3.2 (9.1TB) - Already assigned to H:
REM Disk 5: UnionSine USB3.2 (12.73TB) - Will assign to D:
REM Disk 4: Seagate Portable (3.64TB) - Will assign to E:
REM Disk 3: Seagate Portable (4.55TB) - Will assign to F:
REM Disk 6: SanDisk Ultra (57GB) - Will assign to G:

echo Listing all volumes first...
(
echo list volume
echo exit
) | diskpart

echo.
echo Now assigning drive letters...
echo.

REM For Disk 5 (UnionSine 12.73TB) -> D:
(
echo select disk 5
echo select partition 2
echo active
echo assign letter=D
echo exit
) | diskpart

echo.
echo Disk 5 assignment attempted. Trying Disk 4...
echo.

REM For Disk 4 (Seagate 3.64TB) -> E:
(
echo select disk 4
echo select partition 2
echo active
echo assign letter=E
echo exit
) | diskpart

echo.
echo Disk 4 assignment attempted. Trying Disk 3...
echo.

REM For Disk 3 (Seagate 4.55TB) -> F:
(
echo select disk 3
echo select partition 2
echo active
echo assign letter=F
echo exit
) | diskpart

echo.
echo Disk 3 assignment attempted. Trying Disk 6...
echo.

REM For Disk 6 (SanDisk 57GB) -> G:
(
echo select disk 6
echo select partition 1
echo active
echo assign letter=G
echo exit
) | diskpart

echo.
echo Drive letter assignment complete!
echo.
echo Checking assigned drives:
wmic logicaldisk get deviceid,volumename,size
echo.
pause 